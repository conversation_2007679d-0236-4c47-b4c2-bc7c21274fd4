/**
 * Cloud Function: deleteFiles
 *
 * 1) Determines whether the target file is a duplicate by **reading** all
 *    candidate docs with the same hash (or name) and filtering client-side.
 *    – No composite “== / !=” indexes required (option B).
 * 2) If the file is *not* a duplicate, decrements **Storage Used** on both
 *    the user and project docs inside a Firestore transaction.
 * 3) Deletes the file’s Firestore document, any related comments/likes,
 *    and (optionally) the object in Cloud Storage.
 */

const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.deleteFiles = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError(
            'permission-denied',
            'User must be authenticated to delete files.'
        );
    }

    const uniqueUrl = data.uniqueUrl;      // URL of the file to delete
    let filePath = data.filePath;       // Optional explicit storage path

    if (!uniqueUrl) {
        throw new functions.https.HttpsError(
            'invalid-argument',
            'The function must be called with a unique URL.'
        );
    }

    try {
        const db = admin.firestore();

        // ─────────────────────────────────────────────────────────────
        // Locate the file’s document in the “data” collection
        // ─────────────────────────────────────────────────────────────
        const querySnapshot = await db
            .collection('data')
            .where('File URL', '==', uniqueUrl)
            .limit(1)
            .get();

        if (querySnapshot.empty) {
            throw new functions.https.HttpsError(
                'not-found',
                'No data found with the provided URL.'
            );
        }

        const document = querySnapshot.docs[0];
        const documentId = document.id;
        const docData = document.data();

        const projectId = docData.projectID;
        const uid = docData['User ID'];

        // ─────────────────────────────────────────────────────────────
        // Authorisation check: file owner OR project owner, and must be BookBranch+
        // ─────────────────────────────────────────────────────────────
        const projectDoc = await db.collection('projects').doc(projectId).get();
        const projectOwner = projectDoc.exists ? projectDoc.data().uid : null;

        const userDoc = await db.collection('users').doc(context.auth.uid).get();
        const userPlan = userDoc.exists ? userDoc.data()['Account Type'] : null;

        if (
            (uid !== context.auth.uid && projectOwner !== context.auth.uid) ||
            userPlan !== 'BookBranch+'
        ) {
            throw new functions.https.HttpsError(
                'permission-denied',
                'User does not have permission to delete this file.'
            );
        }

        // ─────────────────────────────────────────────────────────────
        // 1) Duplicate-detection  (Option B: no composite indexes)
        // ─────────────────────────────────────────────────────────────
        const fileHash = docData.fileHash || null;
        const fileName = docData.fileName || docData.displayName || '';
        const fileSize = Number(docData['File Size'] || 0);

        let isDuplicate = false;

        // — a) Match by hash (strongest) —–––––––––––––––––––––––––––––
        if (fileHash) {
            // Fetch *all* docs with same hash and filter client-side
            const hashCandidates = await db
                .collection('data')
                .where('fileHash', '==', fileHash)
                .get();

            const otherDataDocsWithSameHash = hashCandidates.docs.filter(
                //(d) => d.data()['File URL'] !== uniqueUrl
                (d) => d.id !== documentId
            );

            const discussionFilesWithSameHash = await db
                .collection('discussionFiles')
                .where('fileHash', '==', fileHash)
                .get();

            isDuplicate =
                otherDataDocsWithSameHash.length > 0 ||
                discussionFilesWithSameHash.size > 0;
        }

        // — b) Fallback: match by name + size —––––––––––––––––––––––––
        if (!isDuplicate && fileName) {
            const nameCandidates = await db
                .collection('data')
                .where('fileName', '==', fileName)
                .get();

            const otherDataDocsWithSameName = nameCandidates.docs.filter(
                //(d) => d.data()['File URL'] !== uniqueUrl
                (d) => d.id !== documentId
            );

            const discussionFilesWithSameName = await db
                .collection('discussionFiles')
                .where('fileName', '==', fileName)
                .get();

            const potentialDuplicate =
                otherDataDocsWithSameName.length > 0 ||
                discussionFilesWithSameName.size > 0;

            if (potentialDuplicate) {
                // Confirm same size → duplicate
                let sizeMatch = false;

                for (const d of otherDataDocsWithSameName) {
                    if (Number(d.data()['File Size'] || 0) === fileSize && fileSize > 0) {
                        sizeMatch = true;
                        break;
                    }
                }
                if (!sizeMatch) {
                    for (const d of discussionFilesWithSameName.docs) {
                        if (Number(d.data()['fileSize'] || 0) === fileSize && fileSize > 0) {
                            sizeMatch = true;
                            break;
                        }
                    }
                }
                isDuplicate = sizeMatch;
            }
        }

        // ─────────────────────────────────────────────────────────────
        // 2) Transaction – Update storage counters (if NOT duplicate)
        // ─────────────────────────────────────────────────────────────
        const userRef = db.collection('users').doc(uid);
        const projectRef = db.collection('projects').doc(projectId);

        await db.runTransaction(async (tx) => {
            const userSnap = await tx.get(userRef);
            const projectSnap = await tx.get(projectRef);

            if (!userSnap.exists || !projectSnap.exists) {
                throw new Error('User or project document does not exist');
            }

            if (!isDuplicate) {
                const currentUserStorage = Number(userSnap.data()['Storage Used'] || 0);
                const currentProjectStorage = Number(projectSnap.data()['Storage Used'] || 0);

                tx.update(userRef, { 'Storage Used': Math.max(0, currentUserStorage - fileSize) });
                tx.update(projectRef, { 'Storage Used': Math.max(0, currentProjectStorage - fileSize) });
            }
        });

        // ─────────────────────────────────────────────────────────────
        // 3) Delete associated comments / likes
        // ─────────────────────────────────────────────────────────────
        await deleteAssociatedCommentsAndSubcomments(db, uniqueUrl);

        // Delete top-level comment/like convenience docs
        const commentCollections = [
            'fileComments',
            'likedFileComments',
            'dislikedFileComments',
            'likedNestedFileComments',
            'dislikedNestedFileComments',
        ];

        await Promise.all(
            commentCollections.map(async (col) => {
                const snap = await db.collection(col).where('fileUrl', '==', uniqueUrl).get();
                snap.docs.forEach((d) => d.ref.delete());
            })
        );

        // Delete the main “data” document
        await document.ref.delete();

        // ─────────────────────────────────────────────────────────────
        // 4) Delete the actual object in Cloud Storage (optional)
        // ─────────────────────────────────────────────────────────────
        const bucket = admin.storage().bucket();

        if (!filePath) {
            // Extract the path from the download URL if not supplied
            const baseUrl =
                'https://firebasestorage.googleapis.com/v0/b/bookbox-5153e.appspot.com/o/';
            const start = uniqueUrl.indexOf(baseUrl);
            const end = uniqueUrl.indexOf('?', start + baseUrl.length);
            if (start !== -1 && end !== -1) {
                filePath = decodeURIComponent(
                    uniqueUrl.substring(start + baseUrl.length, end).replace(/%2F/g, '/')
                );
            }
        }

        if (filePath) {
            try {
                await bucket.file(filePath).delete();
            } catch (err) {
                console.warn('File document deleted, but object could not be removed:', err);
            }
        }

        return { result: 'File deleted successfully', documentId };
    } catch (err) {
        console.error('Error in deleteFiles:', err);
        throw new functions.https.HttpsError(
            'internal',
            'An error occurred while processing your request.',
            err
        );
    }
});

// ──────────────────────────────────────────────────────────────────
// Helper: delete all nested subcollections for each file comment
// ──────────────────────────────────────────────────────────────────
async function deleteAssociatedCommentsAndSubcomments(db, fileUrl) {
    const fileCommentsSnapshot = await db
        .collection('fileComments')
        .where('fileUrl', '==', fileUrl)
        .get();

    for (const doc of fileCommentsSnapshot.docs) {
        await deleteNestedCollections(doc.ref);
        await doc.ref.delete();
    }
}

async function deleteNestedCollections(documentRef) {
    const subcollections = await documentRef.listCollections();
    for (const collection of subcollections) {
        const docsSnapshot = await collection.get();
        await Promise.all(docsSnapshot.docs.map((d) => d.ref.delete()));
        docsSnapshot.docs.forEach((d) => deleteNestedCollections(d.ref));
    }
}
