const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { Storage } = require('@google-cloud/storage');

exports.deleteProject = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        console.error('Authentication error: User must be authenticated.');
        throw new functions.https.HttpsError('permission-denied', 'User must be authenticated.');
    }

    const projectID = data.projectID;
    const db = admin.firestore();
    const projectDocRef = db.collection('projects').doc(projectID);
    const userDocRef = db.collection('users').doc(context.auth.uid);
    const subscriptionsRef = db.collection('projectSubscriptions');

    const storage = admin.storage();
    const bucket = storage.bucket();

    // cache before deleting
    const projectDocSnapshot = await projectDocRef.get();
    const thumbnailUrl = projectDocSnapshot.data()['thumbnail'];

    let projectStorage = 0;
    if (!projectDocSnapshot.exists) {
        console.error(`Project with ID '${projectID}' not found.`);
    } else {
        projectStorage = projectDocSnapshot.data()['Storage Used'];
    }

    const fileUrls = [];
    const discussionFileUrls = [];


    // update 'Project Count' in users collection
    await userDocRef.update({
        'Project Count': admin.firestore.FieldValue.increment(-1)
    });

    // We'll calculate the actual storage to decrement after checking for duplicate files
    let actualStorageToDecrement = 0;

    // remove all references to projectID in 'projectSubscriptions' collection
    try {
        const querySnapshot = await subscriptionsRef.get();
        const updatePromises = [];

        querySnapshot.forEach(doc => {
            const docData = doc.data();
            if (docData[projectID]) {
                // Using FieldValue.delete() to remove the key from the document
                const updatePromise = doc.ref.update({
                    [projectID]: admin.firestore.FieldValue.delete()
                });
                updatePromises.push(updatePromise);
            }
        });

        await Promise.all(updatePromises);
        console.log(`Removed projectID '${projectID}' from all relevant projectSubscriptions documents.`);
    } catch (error) {
        console.error('Error processing project subscriptions:', error);
        throw new functions.https.HttpsError('internal', 'Failed to process project subscriptions.', error.toString());
    }

    // reference associated metadata
    const applications = await db.collection('applications').where('projectID', '==', projectID).get();
    const fileData = await db.collection('data').where('projectID', '==', projectID).get();
    const discussionFiles = await db.collection('discussionFiles').where('projectID', '==', projectID).get();
    const likeDiscussions = await db.collection('likedDiscussions').where('projectID', '==', projectID).get();
    const dislikedDiscussions = await db.collection('dislikedDiscussions').where('projectID', '==', projectID).get();
    const likedDiscussionComments = await db.collection('likedDiscussionComments').where('projectID', '==', projectID).get();
    const dislikedDiscussionComments = await db.collection('dislikedDiscussionComments').where('projectID', '==', projectID).get();
    const likedNestedDiscussionComments = await db.collection('likedNestedDiscussionComments').where('projectID', '==', projectID).get();
    const dislikedNestedDiscussionComments = await db.collection('dislikedNestedDiscussionComments').where('projectID', '==', projectID).get();
    const likedFileComments = await db.collection('likedFileComments').where('projectID', '==', projectID).get();
    const dislikedFileComments = await db.collection('dislikedFileComments').where('projectID', '==', projectID).get();
    const likedNestedFileComments = await db.collection('likedNestedFileComments').where('projectID', '==', projectID).get();
    const dislikedNestedFileComments = await db.collection('dislikedNestedFileComments').where('projectID', '==', projectID).get();
    const notifications = await db.collection('notifications').where('projectID', '==', projectID).get();

    // delete all associated metadata

    if (!applications.empty) {
        applications.docs.forEach(doc => doc.ref.delete());
    }

    // Process file data and check for duplicates
    if (!fileData.empty) {
        for (const doc of fileData.docs) {
            const fileUrl = doc.data()['File URL'];
            const fileSize = Number(doc.data()['File Size'] || 0);
            const fileHash = doc.data()['fileHash'];
            fileUrls.push(fileUrl);

            // Check if this file is a duplicate
            let isDuplicate = false;

            if (fileHash) {
                console.log(`Checking if file with hash ${fileHash} exists elsewhere`);

                // Check if this file URL exists in other documents in the 'data' collection
                const otherDataDocsWithSameHash = await db.collection('data')
                    .where('fileHash', '==', fileHash)
                    .where('File URL', '!=', fileUrl)
                    .where('projectID', '!=', projectID)
                    .get();

                // Check if this file URL exists in the 'discussionFiles' collection
                const discussionFilesWithSameHash = await db.collection('discussionFiles')
                    .where('fileHash', '==', fileHash)
                    .where('projectID', '!=', projectID)
                    .get();

                // If the file exists elsewhere, it's a duplicate
                isDuplicate = otherDataDocsWithSameHash.size > 0 || discussionFilesWithSameHash.size > 0;

                console.log(`File is ${isDuplicate ? 'a duplicate' : 'not a duplicate'}`);
            } else {
                console.log('No file hash found, treating as a non-duplicate file');
            }

            // Only count non-duplicate files towards storage decrement
            if (!isDuplicate) {
                actualStorageToDecrement += fileSize;
            }
        }
        fileData.docs.forEach(doc => doc.ref.delete());
    }

    // Process discussion files and check for duplicates
    if (!discussionFiles.empty) {
        for (const doc of discussionFiles.docs) {
            const fileUrl = doc.data()['fileUrl'];
            const fileSize = Number(doc.data()['fileSize'] || 0);
            const fileHash = doc.data()['fileHash'];
            discussionFileUrls.push(fileUrl);

            // Check if this file is a duplicate
            let isDuplicate = false;

            if (fileHash) {
                console.log(`Checking if discussion file with hash ${fileHash} exists elsewhere`);

                // Check if this file URL exists in other documents in the 'discussionFiles' collection
                const otherDiscussionFilesWithSameHash = await db.collection('discussionFiles')
                    .where('fileHash', '==', fileHash)
                    .where('fileUrl', '!=', fileUrl)
                    .where('projectID', '!=', projectID)
                    .get();

                // Check if this file URL exists in the 'data' collection
                const dataDocsWithSameHash = await db.collection('data')
                    .where('fileHash', '==', fileHash)
                    .where('projectID', '!=', projectID)
                    .get();

                // If the file exists elsewhere, it's a duplicate
                isDuplicate = otherDiscussionFilesWithSameHash.size > 0 || dataDocsWithSameHash.size > 0;

                console.log(`Discussion file is ${isDuplicate ? 'a duplicate' : 'not a duplicate'}`);
            } else {
                console.log('No file hash found for discussion file, treating as a non-duplicate');
            }

            // Only count non-duplicate files towards storage decrement
            if (!isDuplicate) {
                actualStorageToDecrement += fileSize;
            }
        }
        discussionFiles.docs.forEach(doc => doc.ref.delete());
    }

    if (!likeDiscussions.empty) {
        likeDiscussions.docs.forEach(doc => doc.ref.delete());
    }

    if (!dislikedDiscussions.empty) {
        dislikedDiscussions.docs.forEach(doc => doc.ref.delete());
    }

    if (!likedDiscussionComments.empty) {
        likedDiscussionComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!dislikedDiscussionComments.empty) {
        dislikedDiscussionComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!likedNestedDiscussionComments.empty) {
        likedNestedDiscussionComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!dislikedNestedDiscussionComments.empty) {
        dislikedNestedDiscussionComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!likedFileComments.empty) {
        likedFileComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!dislikedFileComments.empty) {
        dislikedFileComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!likedNestedFileComments.empty) {
        likedNestedFileComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!dislikedNestedFileComments.empty) {
        dislikedNestedFileComments.docs.forEach(doc => doc.ref.delete());
    }

    if (!notifications.empty) {
        notifications.docs.forEach(doc => doc.ref.delete());
    }






    //----------------------------------------
    // first delete the relevant document from 'projects' including subcollections

    try {
        // Update the user's storage usage with the actual storage to decrement
        console.log(`Actual storage to decrement: ${actualStorageToDecrement} (out of ${projectStorage})`);
        await userDocRef.update({
            'Storage Used': admin.firestore.FieldValue.increment(-actualStorageToDecrement)
        });

        await deleteNestedCollections(projectDocRef);
        await projectDocRef.delete();
        console.log(`Project and all related data deleted successfully for projectID: ${projectID}`);

        // the following 2 are multiple documents with potentially nested collections
        // so we need to handle them separately
        // as opposed to 'projects' which is a single document
        const discussions = await db.collection('discussions').where('projectID', '==', projectID).get();
        for (const doc of discussions.docs) {
            await deleteNestedCollections(doc.ref);
            await doc.ref.delete();
        }
        const fileComments = await db.collection('fileComments').where('projectID', '==', projectID).get();
        for (const doc of fileComments.docs) {
            await deleteNestedCollections(doc.ref);
            await doc.ref.delete();
        }

    } catch (error) {
        console.error('Error deleting project:', error);
        throw new functions.https.HttpsError('internal', 'Failed to delete project and related files.', error.toString());
    }

    //----------------------------------------


    try {
        // Function to delete a file based on its URL
        async function deleteFile(fileUrl) {
            if (!fileUrl) {
                console.error('Received undefined fileUrl.');
                throw new Error('Received undefined fileUrl.');
            }

            // Remove base URL and extract the path directly
            const baseUrl = 'https://firebasestorage.googleapis.com/v0/b/bookbox-5153e.appspot.com/o/';
            const pathStartIndex = fileUrl.indexOf(baseUrl) + baseUrl.length;
            const pathEndIndex = fileUrl.indexOf('?');

            if (pathStartIndex === -1 || pathEndIndex === -1) {
                throw new Error(`URL does not contain expected base or query string: ${fileUrl}`);
            }

            // Extract the file path and replace encoded forward slashes to actual forward slashes
            let filePath = fileUrl.substring(pathStartIndex, pathEndIndex).replace(/%2F/g, '/');
            filePath = decodeURIComponent(filePath);  // Decode the path to handle special characters

            const file = bucket.file(filePath);
            await file.delete();
            console.log(`Deleted file at: ${filePath}`);
        }

        // Log file URLs before deletion
        console.log('File URLs:', fileUrls);
        console.log('Discussion File URLs:', discussionFileUrls);

        // delete project thumbnail
        if (thumbnailUrl) {
            try {
                await deleteFile(thumbnailUrl);
            } catch (error) {
                console.error('Failed to delete project thumbnail:', error);
            }
        }

        // Loop through the fileUrls and delete each
        for (const fileUrl of fileUrls) {
            await deleteFile(fileUrl);
        }

        // Loop through the discussionFileUrls and delete each
        for (const fileUrl of discussionFileUrls) {
            await deleteFile(fileUrl);
        }

        return { result: 'Files deleted successfully.' };
    } catch (error) {
        console.error('Failed to delete files:', error);
        throw new functions.https.HttpsError('internal', 'Failed to delete files.', error);
    }




});

// do not touch this function
async function deleteNestedCollections(docRef) {
    const subcollections = await docRef.listCollections();
    for (const collection of subcollections) {
        const docs = await collection.listDocuments();
        const docPromises = docs.map(async (doc) => {
            await deleteNestedCollections(doc);
            await doc.delete();
        });
        await Promise.all(docPromises);
    }
}


