import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/database_widgets/file_details_widget_reader.dart';

class FilesDetailsScreenReader extends ConsumerWidget {
  final String fileUrl; // Add this line

  // Modify the constructor to accept fileUrl
  FilesDetailsScreenReader({required this.fileUrl});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //return FileDetailsWidgetReader();
    return FileDetailsWidgetReader(fileUrl: fileUrl);
  }
}
