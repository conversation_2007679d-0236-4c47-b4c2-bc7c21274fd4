import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/database_screens/file_details_screen_reader.dart';
import 'package:web_app/database/database_widgets/elevatedButtons.dart';
import 'package:web_app/database/database_widgets/subsections_widget.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class EPublishingFilesWidgetReader extends ConsumerWidget {
  static const String routeName = '/databaseChapterDetails';
  final ElevatedButtons elevatedButton = ElevatedButtons();
  final SubsectionsWidget widget = SubsectionsWidget();

  final DeleteData deleteData = DeleteData();
  final FetchDatabase fetch = FetchDatabase();

  final Map<String, IconData> fileTypeIcons = {
    'Image': Icons.image, // Icon for image files
    'PDF': Icons.picture_as_pdf, // Icon for PDF files
    'Video': Icons.videocam, // Icon for video files
    // Add more file types and their icons as needed
  };

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentChapterVar = ref.watch(currentEPublishingChapter);
    final currentSubsectionVar = ref.watch(currentEPublishingSubsection);
    final currentProjectIDVar = ref.watch(currentProjectID);
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Database',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: FutureBuilder<List<FileMetadata>>(
        future: FileMetadata.fetchFileMetadataEPublishing(
            currentChapterVar, currentSubsectionVar, currentProjectIDVar),
        builder:
            (BuildContext context, AsyncSnapshot<List<FileMetadata>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          } else if (snapshot.hasData) {
            if (snapshot.data!.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(maxWidth: 400),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: globals.bookBranchGreen.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.all(24),
                    child: const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.file_copy_outlined,
                          size: 70,
                          color: Color.fromARGB(178, 44, 148, 44),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No Files Available',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'You have not uploaded any files in this section yet.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color.fromARGB(178, 102, 102, 102),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Use the upload button to add files to this section.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color.fromARGB(178, 102, 102, 102),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              return kIsWeb
                  ? buildGridView(snapshot.data!, ref)
                  : buildListViewMobile(
                      snapshot.data!, ref); // Use ListView for mobile
            }
          } else {
            return const Center(
              child: Text('No data available'),
            );
          }
        },
      ),
    );
  }

  Widget buildGridView(List<FileMetadata> files, WidgetRef ref) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 3 / 1.2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: files.length,
      itemBuilder: (context, index) {
        FileMetadata metadata = files[index];
        IconData iconData =
            fileTypeIcons[metadata.fileType] ?? Icons.file_present;
        return buildFileCard(context, ref, metadata, iconData);
      },
    );
  }

  Widget buildListViewMobile(List<FileMetadata> files, WidgetRef ref) {
    return ListView.builder(
      itemCount: files.length,
      itemBuilder: (context, index) {
        FileMetadata metadata = files[index];
        IconData iconData =
            fileTypeIcons[metadata.fileType] ?? Icons.file_present;
        return buildFileCardMobile(context, ref, metadata, iconData);
      },
    );
  }

  Widget buildFileCard(BuildContext context, WidgetRef ref,
      FileMetadata metadata, IconData iconData) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () {
          String fileUrl = metadata.fileURL;
          ref.read(chosenFileUrl.notifier).state = fileUrl;

          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return FilesDetailsScreenReader(
                fileUrl: fileUrl); // Pass fileUrl here
          }));
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                metadata.displayName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  metadata.fileDescription,
                  overflow: TextOverflow.ellipsis, // Add this
                  maxLines:
                      3, // Set max lines or adjust according to your needs
                  style: const TextStyle(
                      fontSize: 14), // Optional: Adjust text style
                ),
              ),
              const Spacer(), // Pushes the icon to the bottom of the card
              Icon(iconData, size: 50)
            ],
          ),
        ),
      ),
    );
  }

  Widget buildFileCardMobile(BuildContext context, WidgetRef ref,
      FileMetadata metadata, IconData iconData) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      child: Stack(
        children: [
          // Decorative element - top gradient bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 6,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    bookBranchGreen,
                    Color.fromARGB(178, 44, 148, 44),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
          // Decorative element - corner accent
          Positioned(
            top: 6,
            right: 0,
            child: Container(
              height: 24,
              width: 24,
              decoration: const BoxDecoration(
                color: lightGreen,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                ),
              ),
            ),
          ),
          // Main content
          InkWell(
            onTap: () {
              String fileUrl = metadata.fileURL;
              ref.read(chosenFileUrl.notifier).state = fileUrl;

              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return FilesDetailsScreenReader(
                    fileUrl: fileUrl); // Pass fileUrl here
              }));
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // File type icon in a container
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: lightGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          iconData,
                          color: bookBranchGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // File name and description in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // File name
                            Text(
                              metadata.displayName,
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // File description
                            Text(
                              metadata.fileDescription,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                height: 1.4,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
