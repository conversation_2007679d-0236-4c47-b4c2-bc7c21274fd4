import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_widgets/signup_widget.dart';
import 'package:web_app/dartMain/dartMain_widgets/login_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:animated_text_kit/animated_text_kit.dart';

// Custom animated text that can be positioned anywhere
class CustomAnimatedText extends AnimatedText {
  final Alignment alignment;
  // static const TextStyle defaultTextStyle = TextStyle(
  //   fontSize: 50,
  //   fontFamily: 'Montserrat',
  //   fontWeight: FontWeight.w300, // Lighter font weight
  //   color: Colors.white,
  //   shadows: [
  //     Shadow(
  //       blurRadius: 10.0,
  //       color: Colors.black38,
  //       offset: Offset(2, 2),
  //     ),
  //   ],
  // );
  static const TextStyle defaultTextStyle = TextStyle(
    fontSize: 50,
    fontFamily: 'Montserrat',
    fontWeight: FontWeight.bold, // bold
    fontStyle: FontStyle.italic, // italic
    color: Colors.white,
    shadows: [
      // still keep the drop-shadow
      Shadow(blurRadius: 4, color: Colors.black38, offset: Offset(5, 5)),
    ],
  );

  CustomAnimatedText(
    String text, {
    required this.alignment,
    required super.textAlign,
    required super.duration,
    TextStyle? textStyle,
  }) : super(
          text: text,
          textStyle: textStyle ?? defaultTextStyle,
        );

  late Animation<double> _fadeIn, _fadeOut;

  @override
  void initAnimation(AnimationController controller) {
    // Longer fade-in animation (0% to 30% of the duration)
    _fadeIn = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.0, 0.3, curve: Curves.easeIn),
      ),
    );

    // Longer fade-out animation (70% to 100% of the duration)
    _fadeOut = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
    );
  }

  @override
  Widget animatedBuilder(BuildContext context, Widget? child) {
    return Align(
      alignment: alignment,
      child: Opacity(
        opacity: _fadeIn.value * (1.0 - _fadeOut.value),
        child: Text(
          text,
          textAlign: textAlign,
          style: textStyle,
        ),
      ),
    );
  }

  @override
  Widget completeText(BuildContext context) => const SizedBox.shrink();

  @override
  Duration get remaining => Duration.zero;
}

// Custom painter for drawing a branch-like decoration
class BranchPainter extends CustomPainter {
  final double strokeWidth;

  BranchPainter({this.strokeWidth = 2.0});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Start at the bottom
    path.moveTo(size.width / 2, size.height);

    // Draw the main branch
    path.quadraticBezierTo(size.width / 2 - 20, size.height * 0.7,
        size.width / 2 - 10, size.height * 0.5);

    path.quadraticBezierTo(
        size.width / 2, size.height * 0.3, size.width / 3, size.height * 0.1);

    // Draw some smaller branches
    // Branch 1
    path.moveTo(size.width / 2 - 10, size.height * 0.6);
    path.quadraticBezierTo(
        size.width / 3, size.height * 0.55, size.width / 4, size.height * 0.45);

    // Branch 2
    path.moveTo(size.width / 2 - 5, size.height * 0.4);
    path.quadraticBezierTo(size.width / 1.8, size.height * 0.35,
        size.width / 1.5, size.height * 0.25);

    // Branch 3
    path.moveTo(size.width / 2.5, size.height * 0.3);
    path.quadraticBezierTo(size.width / 3, size.height * 0.25, size.width / 3.5,
        size.height * 0.15);

    // Add some leaf details
    final leafPath = Path();

    // Leaf 1
    leafPath.moveTo(size.width / 4, size.height * 0.45);
    leafPath.quadraticBezierTo(size.width / 5, size.height * 0.42,
        size.width / 4.5, size.height * 0.4);

    // Leaf 2
    leafPath.moveTo(size.width / 1.5, size.height * 0.25);
    leafPath.quadraticBezierTo(size.width / 1.4, size.height * 0.22,
        size.width / 1.45, size.height * 0.2);

    // Leaf 3
    leafPath.moveTo(size.width / 3.5, size.height * 0.15);
    leafPath.quadraticBezierTo(size.width / 3.3, size.height * 0.12,
        size.width / 3.4, size.height * 0.1);

    canvas.drawPath(path, paint);
    canvas.drawPath(leafPath, paint..strokeWidth = strokeWidth * 0.7);
  }

  @override
  bool shouldRepaint(BranchPainter oldDelegate) =>
      oldDelegate.strokeWidth != strokeWidth;
}

class AuthenticationWidget extends ConsumerStatefulWidget {
  const AuthenticationWidget({super.key});
  static const String routeName = '/Authentication';

  @override
  ConsumerState<AuthenticationWidget> createState() => _AuthenticationWidget();
}

class _AuthenticationWidget extends ConsumerState<AuthenticationWidget> {
  AuthenticationLogic authObject = AuthenticationLogic();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background image
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/signInBackground.png'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          //Content
          SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Top section with animated 'BookBranch' text
                Padding(
                  padding: const EdgeInsets.only(
                      top: 60, left: 20, right: 20), // Top padding
                  child: Column(
                    children: [
                      // Animated text that moves around
                      SizedBox(
                        width: double.infinity,
                        height: 200,
                        child: Stack(
                          children: [
                            // Title with position-changing animation
                            AnimatedTextKit(
                              animatedTexts: [
                                // Top position
                                CustomAnimatedText(
                                  'BookBranch',
                                  textAlign: TextAlign.center,
                                  alignment: Alignment.topCenter,
                                  duration: const Duration(seconds: 5),
                                ),
                                // Middle-left position
                                CustomAnimatedText(
                                  'BookBranch',
                                  textAlign: TextAlign.left,
                                  alignment: Alignment.centerLeft,
                                  duration: const Duration(seconds: 5),
                                ),
                                // Bottom position
                                CustomAnimatedText(
                                  'BookBranch',
                                  textAlign: TextAlign.center,
                                  alignment: Alignment.bottomCenter,
                                  duration: const Duration(seconds: 5),
                                ),
                                // Middle-right position
                                CustomAnimatedText(
                                  'BookBranch',
                                  textAlign: TextAlign.right,
                                  alignment: Alignment.centerRight,
                                  duration: const Duration(seconds: 5),
                                ),
                              ],
                              isRepeatingAnimation: true,
                              repeatForever: true,
                              onTap: () {
                                // Optional: Add an easter egg when tapping the text
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Welcome to BookBranch!'),
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Bottom section with buttons
                Padding(
                  padding: const EdgeInsets.only(bottom: 60), // Bottom padding
                  child: Column(
                    children: [
                      _buildAnimatedButton(
                          context, "Sign Up", globals.bookBranchGreen, () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const SignUpPage()),
                        );
                      }),
                      const SizedBox(height: 20),
                      _buildAnimatedButton(
                          context, "Sign In", globals.bookBranchGreen, () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const LoginWidget()),
                        );
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Animated button with hover effects
  Widget _buildAnimatedButton(
      BuildContext context, String text, Color color, VoidCallback onPressed) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 1.0, end: 1.0),
      duration: const Duration(milliseconds: 300),
      builder: (context, scale, child) {
        return MouseRegion(
          onEnter: (_) => setState(() {}),
          onExit: (_) => setState(() {}),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: MediaQuery.of(context).size.width * 0.8,
            height: 55,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                foregroundColor: color,
                backgroundColor: Colors.white,
                elevation: 5,
                shadowColor: color.withAlpha(100),
                side: BorderSide(color: color, width: 1.5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                  letterSpacing: 1.2,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Handle Google sign-in with proper context handling
  Future<void> handleSignInWithGoogle() async {
    try {
      User? user = await authObject.signInWithGoogle(ref);

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      if (user != null) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to sign in with Google. Try again.'),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
        ),
      );
    }
  }
}
