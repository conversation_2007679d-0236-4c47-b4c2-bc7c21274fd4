import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/forgot_password_screen.dart';
import 'package:web_app/main.dart';
import 'package:getwidget/getwidget.dart';

class LoginWidget extends ConsumerStatefulWidget {
  const LoginWidget({Key? key}) : super(key: key);

  @override
  _LoginWidgetState createState() => _LoginWidgetState();
}

class _LoginWidgetState extends ConsumerState<LoginWidget> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  AuthenticationLogic authObject = AuthenticationLogic();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(isLoadingProvider); // Listen to loading state

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        leading: IconButton(
          icon:
              Icon(Icons.arrow_back, color: globals.bookBranchGreen, size: 24),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Sign In',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
      ),
      body: Stack(
        children: [
          // Main scrollable content
          SingleChildScrollView(
            padding:
                const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 30),
                // Enhanced welcome section
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.8, end: 1.0),
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.easeOutQuad,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value,
                            child: const Text(
                              'Welcome back!',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                                letterSpacing: 0.5,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      Text(
                        "Sign in to continue where you left off.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade700,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
                _textField("Email", _emailController, false),
                const SizedBox(height: 20),
                _passwordField("Password", _passwordController, context),
                const SizedBox(height: 40),
                _loginButton(context),
                const SizedBox(height: 40),
                // Divider with "or" text
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        height: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                    Text(
                      "or",
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        height: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                // Enhanced Google sign-in button
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: const [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.05),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: buildGoogleSignInButton(() {
                    // Handle Google sign-in with a safer approach
                    _handleGoogleSignIn();
                  }),
                ),
                const SizedBox(height: 30),
                TextButton(
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ForgotPasswordScreen(),
                    ),
                  ),
                  child: Text(
                    'Forgot Password?',
                    style: TextStyle(
                      fontSize: 14,
                      color: globals.bookBranchGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 60),
              ],
            ),
          ),

          // Overlay loading indicator when isLoading is true
          if (isLoading)
            Container(
              color: Colors.black45, // semi-transparent background
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Custom animated loader
                    GFLoader(
                      type: GFLoaderType.circle,
                      loaderColorOne: globals.bookBranchGreen,
                      loaderColorTwo: Colors.white,
                      loaderColorThree: globals.bookBranchGreen,
                      size: 50.0,
                    ),
                    const SizedBox(height: 20),
                    // Animated text
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: const Duration(milliseconds: 500),
                      builder: (context, value, child) {
                        return Opacity(
                          opacity: value,
                          child: Transform.translate(
                            offset: Offset(0, 20 * (1 - value)),
                            child: const Text(
                              'Signing in...',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _textField(
      String label, TextEditingController controller, bool isPassword) {
    return GFTextField(
      controller: controller,
      textAlign: TextAlign.start,
      obscureText: isPassword,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Colors.grey),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.0),
          borderRadius: BorderRadius.circular(4),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: globals.bookBranchGreen, width: 2.0),
          borderRadius: BorderRadius.circular(4),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        border: const OutlineInputBorder(),
      ),
      // Add subtle animation when focusing
      onTap: () {},
      // Add a nice animation when typing
      cursorColor: globals.bookBranchGreen,
    );
  }

  Widget _passwordField(
      String label, TextEditingController controller, BuildContext context) {
    bool obscureText = true;
    ValueNotifier<bool> obscureTextNotifier = ValueNotifier(obscureText);

    return ValueListenableBuilder(
      valueListenable: obscureTextNotifier,
      builder: (context, bool obscure, _) {
        return GFTextField(
          controller: controller,
          textAlign: TextAlign.start,
          obscureText: obscure,
          decoration: InputDecoration(
            labelText: label,
            labelStyle: const TextStyle(color: Colors.grey),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1.0),
              borderRadius: BorderRadius.circular(4),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: globals.bookBranchGreen, width: 2.0),
              borderRadius: BorderRadius.circular(4),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            border: const OutlineInputBorder(),
            suffixIcon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return ScaleTransition(scale: animation, child: child);
              },
              child: IconButton(
                key: ValueKey<bool>(obscure),
                icon: Icon(
                  obscure ? Icons.visibility_off : Icons.visibility,
                  color: obscure ? Colors.grey : globals.bookBranchGreen,
                ),
                onPressed: () =>
                    obscureTextNotifier.value = !obscureTextNotifier.value,
              ),
            ),
          ),
          cursorColor: globals.bookBranchGreen,
        );
      },
    );
  }

  Widget _loginButton(BuildContext context) {
    return GFButton(
      onPressed: () {
        // Implement login functionality
        handleSignIn();
      },
      text: "Sign In",
      textStyle: const TextStyle(
          fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
      color: globals.bookBranchGreen,
      fullWidthButton: true,
      size: GFSize.LARGE,
      shape: GFButtonShape.standard,
      // Add a subtle hover animation
      hoverColor: globals.bookBranchGreen.withAlpha(200),
      // Add a nice elevation effect
      elevation: 2,
      highlightElevation: 4,
      splashColor: const Color.fromRGBO(255, 255, 255, 0.3),
      padding: const EdgeInsets.symmetric(vertical: 10),
    );
  }

  Widget buildGoogleSignInButton(VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Image.asset(
        'assets/google/signin_logo.png',
        width: 240,
        height: 50,
      ),
    );
  }

  void handleSignIn() {
    AuthenticationLogic authObject = AuthenticationLogic();

    authObject.handleSignInEmail(
        _emailController.text, _passwordController.text, ref, context);
    userDidSignInSuccessfully();
  }

  void userDidSignInSuccessfully() {
    // Use a safer approach to handle navigation after authentication
    // by using a one-time check instead of a listener
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    });
  }

  // Handle Google sign-in with proper context handling
  Future<void> _handleGoogleSignIn() async {
    // Set loading to true
    ref.read(isLoadingProvider.notifier).state = true;

    try {
      User? user = await authObject.signInWithGoogle(ref);

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      if (user != null) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to sign in with Google. Try again.'),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
        ),
      );
    } finally {
      // Make sure to set loading to false if widget is still mounted
      if (mounted) {
        ref.read(isLoadingProvider.notifier).state = false;
      }
    }
  }
}
