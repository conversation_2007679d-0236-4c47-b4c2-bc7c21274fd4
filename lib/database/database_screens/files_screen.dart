import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_widgets/elevatedButtons.dart';
import 'package:web_app/database/database_widgets/subsections_widget.dart';
import 'package:web_app/database/database_widgets/display_files_widget.dart';
import 'package:web_app/database/database_logic/file_selector_logic/file_selector_simplified.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class Files extends ConsumerWidget {
  static const String routeName = '/databaseChapterDetails';
  final ElevatedButtons elevatedButton = ElevatedButtons();
  final SubsectionsWidget widget = SubsectionsWidget();
  final FileSelector fileSelector = FileSelector();
  final UniversalWidgets universals = UniversalWidgets();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Database',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: DisplayFilesWidget(),
      floatingActionButton: Stack(
        alignment: Alignment.bottomRight,
        children: [
          Positioned(
            right: 0,
            child: FloatingActionButton(
              heroTag: 'uploadData',
              backgroundColor: globals.bookBranchGreen,
              foregroundColor: Colors.white,
              highlightElevation: 50,
              tooltip: "Upload Data",
              onPressed: () {
                //fileSelector.createDataUploadDialogue(context, ref);
                universals.showCustomPopupWithCheckbox(
                  context: context,
                  titleText: "Upload Agreement",
                  contentText:
                      "By uploading data, you agree to abide by our Terms of Use.",
                  checkboxText: "I Agree",
                  cancelButtonText: "Cancel",
                  actionButtonText: "Proceed",
                  onActionPressed: () {
                    // Action button pressed
                    fileSelector.createDataUploadDialogue(context, ref,
                        isEPublishing: false);
                  },
                );
              },
              child: const Icon(Icons.add),
            ),
          ),
        ],
      ),
    );
  }
}
