import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';

class FileDetailsScreen extends ConsumerWidget {
  static const String routeName = '/databaseChapterDetails';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WillPopScope(
      onWillPop: () async {
        ref.refresh(fileMetadataProvider);
        return true;
      },
      child: Scaffold(
        body: FileDetailsWidget(fileUrl: ref.watch(chosenFileUrl)),
      ),
    );
  }
}
