import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:web_app/database/database_screens/file_details_screen.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class DisplayFilesWidget extends ConsumerStatefulWidget {
  @override
  _DisplayFilesWidgetState createState() => _DisplayFilesWidgetState();
}

class _DisplayFilesWidgetState extends ConsumerState<DisplayFilesWidget> {
  final DeleteData deleteData = DeleteData();
  final FetchDatabase fetch = FetchDatabase();
  bool _selectionMode = false;
  Set<String> _selectedFiles = {};

  final Map<String, IconData> fileTypeIcons = {
    'Image': Icons.image, // Icon for image files
    'PDF': Icons.picture_as_pdf, // Icon for PDF files
    'Video': Icons.videocam, // Icon for video files
    // Add more file types and their icons as needed
  };

  // Toggle selection mode
  void _toggleSelectionMode() {
    setState(() {
      _selectionMode = !_selectionMode;
      // Clear selections when exiting selection mode
      if (!_selectionMode) {
        _selectedFiles.clear();
      }
    });
  }

  // Select all files
  void _selectAllFiles(List<FileMetadata> files) {
    setState(() {
      for (var file in files) {
        _selectedFiles.add(file.fileURL);
      }
    });
  }

  // Deselect all files
  void _deselectAllFiles() {
    setState(() {
      _selectedFiles.clear();
    });
  }

  // Toggle selection of a file
  void _toggleFileSelection(String fileUrl) {
    setState(() {
      if (_selectedFiles.contains(fileUrl)) {
        _selectedFiles.remove(fileUrl);
      } else {
        _selectedFiles.add(fileUrl);
      }
    });
  }

  // Delete all selected files
  Future<void> _deleteSelectedFiles() async {
    if (_selectedFiles.isEmpty) return;

    // Show confirmation dialog
    bool confirm = await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Files'),
            content: Text(
                'Are you sure you want to delete ${_selectedFiles.length} files?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child:
                    const Text('Delete', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirm) return;

    // Create a copy of the selected files to avoid modification during iteration
    final filesToDelete = Set<String>.from(_selectedFiles);

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text('Deleting files...')));
    }

    // Delete each file
    FirebaseFunctions functions = FirebaseFunctions.instance;
    HttpsCallable callable = functions.httpsCallable('deleteFiles');
    int deletedCount = 0;

    for (String fileUrl in filesToDelete) {
      try {
        // Check if the file exists in our current files list
        try {
          // Just check if the file exists, we don't need the metadata anymore
          _currentFiles.firstWhere(
            (file) => file.fileURL == fileUrl,
          );
        } catch (e) {
          // File not found in current files, try to delete with just the URL
          debugPrint('File not found in current files: $fileUrl');

          // Try to delete with just the URL and a null filePath
          try {
            // The cloud function extracts the path from the URL, so we don't need to provide filePath
            await callable.call({
              'uniqueUrl': fileUrl,
              // No need to provide filePath as the cloud function extracts it from the URL
            });
            deletedCount++;
          } catch (e) {
            debugPrint('Error deleting file with just URL: $e');
          }
          continue;
        }

        // The file exists in our list, so delete it
        try {
          await callable.call({
            'uniqueUrl': fileUrl,
            // The cloud function extracts the path from the URL, so filePath is optional
          });

          // Remove from current files list
          setState(() {
            _currentFiles.removeWhere((file) => file.fileURL == fileUrl);
          });

          // Also try to remove from fileListProvider if it exists there
          try {
            ref.read(fileListProvider.notifier).state = ref
                .read(fileListProvider)
                .where((file) => file.fileURL != fileUrl)
                .toList();
          } catch (e) {
            // Ignore errors with fileListProvider
          }

          deletedCount++;
        } catch (e) {
          debugPrint('Error deleting file with metadata: $e');
        }
      } catch (e) {
        debugPrint('Error in deletion process: $e');
      }
    }

    // Clear selections and refresh data
    setState(() {
      _selectedFiles.clear();
    });

    ref.invalidate(fileMetadataProvider);
    var _ = ref.refresh(fileMetadataProvider);

    // Show completion message
    if (mounted) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('$deletedCount files deleted')));
    }
  }

  // Variable to store the current files for select all functionality
  List<FileMetadata> _currentFiles = [];

  @override
  Widget build(BuildContext context) {
    final currentChapterVar = ref.watch(currentChapter);
    final currentSubsectionVar = ref.watch(currentSubsection);
    final currentProjectIDVar = ref.watch(currentProjectID);
    ref.watch(fileMetadataProvider);

    return Scaffold(
      appBar: AppBar(
        title: _selectionMode
            ? Text('${_selectedFiles.length} selected')
            : const Text('Files'),
        automaticallyImplyLeading: false,
        actions: [
          // Show select all button when in selection mode
          if (_selectionMode)
            IconButton(
              icon: const Icon(Icons.select_all),
              tooltip: 'Select All',
              onPressed: () => _selectAllFiles(_currentFiles),
            ),
          // Show clear selection button when files are selected
          if (_selectionMode && _selectedFiles.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.deselect),
              tooltip: 'Clear Selection',
              onPressed: _deselectAllFiles,
            ),
          // Show delete button in app bar when in selection mode and files are selected
          if (_selectionMode && _selectedFiles.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Selected',
              onPressed: _deleteSelectedFiles,
            ),
          // Toggle selection mode button
          IconButton(
            icon: Icon(_selectionMode ? Icons.cancel : Icons.select_all),
            tooltip: _selectionMode ? 'Exit Selection Mode' : 'Select Multiple',
            onPressed: _toggleSelectionMode,
          ),
        ],
      ),
      // Show delete button when in selection mode and files are selected
      floatingActionButton: _selectionMode && _selectedFiles.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: _deleteSelectedFiles,
              backgroundColor: Colors.red,
              icon: const Icon(Icons.delete),
              label: Text('Delete ${_selectedFiles.length}'),
            )
          : null,
      body: FutureBuilder<List<FileMetadata>>(
        future: FileMetadata.fetchFileMetadata(
            currentChapterVar, currentSubsectionVar, currentProjectIDVar),
        builder:
            (BuildContext context, AsyncSnapshot<List<FileMetadata>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Text('Error: ${snapshot.error}');
          } else if (snapshot.hasData) {
            if (snapshot.data!.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(maxWidth: 400),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: globals.bookBranchGreen.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.all(24),
                    child: const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.file_copy_outlined,
                          size: 70,
                          color: Color.fromARGB(178, 44, 148, 44),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No Files Available',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'You have not uploaded any files in this section yet.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color.fromARGB(178, 102, 102, 102),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Use the upload button to add files to this section.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color.fromARGB(178, 102, 102, 102),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              // Store current files for select all functionality
              _currentFiles = snapshot.data!;
              return buildListViewMobile(snapshot.data!);
            }
          } else {
            return const Text('No data available');
          }
        },
      ),
    );
  }

  Widget buildListViewMobile(List<FileMetadata> files) {
    return ListView.builder(
      itemCount: files.length,
      itemBuilder: (context, index) {
        FileMetadata metadata = files[index];
        IconData iconData =
            fileTypeIcons[metadata.fileType] ?? Icons.file_present;
        return buildFileCardMobile(context, metadata, iconData);
      },
    );
  }

  Widget buildFileCardMobile(
      BuildContext context, FileMetadata metadata, IconData iconData) {
    // State to track if the deletion is in progress
    final ValueNotifier<bool> isDeleting = ValueNotifier<bool>(false);
    final bool isSelected = _selectedFiles.contains(metadata.fileURL);
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withAlpha(30) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isSelected ? Colors.blue : bookBranchGreen,
          width: isSelected ? 2 : 1.5,
        ),
      ),
      child: Stack(
        children: [
          // Decorative element - top gradient bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 6,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    bookBranchGreen,
                    Color.fromARGB(178, 44, 148, 44),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
          // Decorative element - corner accent
          Positioned(
            top: 6,
            right: 0,
            child: Container(
              height: 24,
              width: 24,
              decoration: const BoxDecoration(
                color: lightGreen,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                ),
              ),
            ),
          ),
          // Main content
          InkWell(
            onTap: () {
              if (_selectionMode) {
                // In selection mode, tap to select/deselect
                setState(() {
                  if (isSelected) {
                    _selectedFiles.remove(metadata.fileURL);
                  } else {
                    _selectedFiles.add(metadata.fileURL);
                  }
                });
              } else {
                // Normal mode, open file details
                ref.read(chosenFileUrl.notifier).state = metadata.fileURL;
                Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => FileDetailsScreen()));
              }
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // File type icon in a container
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: lightGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          iconData,
                          color: bookBranchGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // File name and description in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Show checkbox in selection mode
                            if (_selectionMode)
                              Checkbox(
                                value: isSelected,
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      _selectedFiles.add(metadata.fileURL);
                                    } else {
                                      _selectedFiles.remove(metadata.fileURL);
                                    }
                                  });
                                },
                                activeColor: bookBranchGreen,
                              ),
                            // File name
                            Text(
                              metadata.displayName,
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // File description
                            Text(
                              metadata.fileDescription,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                height: 1.4,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      // Only show delete button in normal mode
                      if (!_selectionMode)
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.grey),
                          onPressed: () async {
                            ref.read(chosenFileUrl.notifier).state =
                                metadata.fileURL;

                            isDeleting.value = true; // Start deleting

                            FirebaseFunctions functions =
                                FirebaseFunctions.instance;
                            HttpsCallable callable =
                                functions.httpsCallable('deleteFiles');

                            try {
                              final result = await callable.call({
                                'uniqueUrl': ref.watch(chosenFileUrl),
                                // The cloud function extracts the path from the URL, so filePath is optional
                              });
                              debugPrint(
                                  "Function called successfully: ${result.data}");

                              // Update the UI to remove the deleted file
                              setState(() {
                                _currentFiles.removeWhere(
                                    (file) => file.fileURL == metadata.fileURL);
                              });

                              // Also update the provider state
                              List<FileMetadata> currentFiles =
                                  ref.read(fileListProvider.notifier).state;
                              ref.read(fileListProvider.notifier).state =
                                  currentFiles
                                      .where((file) =>
                                          file.fileURL != metadata.fileURL)
                                      .toList();

                              // Show a success message
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('File deleted successfully')));
                              }
                            } catch (e) {
                              debugPrint('Error deleting file: $e');
                              // Show an error message
                              if (mounted) {
                                String errorMsg = e.toString();
                                if (errorMsg.length > 100) {
                                  errorMsg = '${errorMsg.substring(0, 100)}...';
                                }
                                ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text(
                                            'Error deleting file: $errorMsg')));
                              }
                            } finally {
                              isDeleting.value = false; // Finish deleting
                            }

                            ref.invalidate(fileMetadataProvider);
                            var _ = ref.refresh(fileMetadataProvider);
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          iconSize: 20,
                        ),
                    ],
                  ),
                  // Loading indicator
                  ValueListenableBuilder<bool>(
                    valueListenable: isDeleting,
                    builder: (context, isDeleting, _) {
                      if (isDeleting) {
                        return const Padding(
                          padding: EdgeInsets.only(top: 10),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
