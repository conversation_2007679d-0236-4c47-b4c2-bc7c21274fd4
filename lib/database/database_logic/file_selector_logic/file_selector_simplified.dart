import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/fetch_database.dart';
import 'package:cloud_functions/cloud_functions.dart';

/// A simplified file selector for mobile platforms that handles file selection
/// and uploading to Firebase Storage with metadata to Firestore.
class FileSelector {
  // Default values
  String selectedFileType = "Image";
  String selectedPrivacy = "Public";

  // Service instances
  final UploadData dataObj = UploadData();
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FetchDatabase fetch = FetchDatabase();

  // Text controllers for file metadata
  final TextEditingController displayName = TextEditingController();
  final TextEditingController fileName = TextEditingController();
  final TextEditingController fileTypeController = TextEditingController();
  final TextEditingController projectDescription = TextEditingController();
  final TextEditingController tags = TextEditingController();
  final TextEditingController projectName = TextEditingController();

  /// Creates a file upload dialog and handles the file selection and upload process
  ///
  /// [context] - The BuildContext for showing dialogs and snackbars
  /// [ref] - The WidgetRef for accessing Riverpod providers
  /// [isEPublishing] - Whether this is for e-publishing (affects which providers are used)
  Future<void> createDataUploadDialogue(BuildContext context, WidgetRef ref,
      {bool isEPublishing = false}) async {
    // Create a list to store selected files
    List<PlatformFile> selectedFiles = [];
    Map<PlatformFile, String> validFiles = {};

    // Show the file selection dialog directly
    await _showFileSelectionDialog(
        context, selectedFiles, validFiles, ref, isEPublishing);
  }

// ─── FileSelector.dart ─────────────────────────────────────────────────────────
  Future<bool> _hasEnoughStorage(int aggregateSize) async {
    final uid = auth.currentUser?.uid;
    if (uid == null) return false; // just in case

    final functions = FirebaseFunctions.instance;
    final HttpsCallable callable = functions.httpsCallable('hasEnoughStorage');

    try {
      final result = await callable.call({
        'fileSize': aggregateSize,
        'userId': uid,
      });

      return result.data['success'] as bool;
    } catch (e) {
      debugPrint('Storage-check failed: $e');
      return false; // be safe and block upload
    }
  }

  /// Processes the actual upload of multiple files after selection
  Future<void> _processMultipleFileUpload(
      BuildContext context, WidgetRef ref, Map<PlatformFile, String> validFiles,
      {bool isEPublishing = false}) async {
    // Check if the widget is still mounted before using context
    if (!context.mounted) return;

    // Check if any files were selected
    if (validFiles.isEmpty) {
      debugPrint("No valid files were selected for upload");
      return; // User canceled or didn't select any valid files
    }

    // ── NEW: sum selected-file sizes and check quota ───────────────
    final totalBytes = validFiles.keys.fold<int>(0, (s, f) => s + f.size);
    final ok = await _hasEnoughStorage(totalBytes);

    if (!ok) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You do not have enough available storage '
                'for these files. Please remove some files or select smaller ones.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return; // abort
    }

    // Debug: Check if files have valid data before uploading
    int filesWithBytes = 0;
    for (var file in validFiles.keys) {
      if (file.bytes != null) {
        filesWithBytes++;
      }
      debugPrint(
          "File to upload: ${file.name}, has bytes: ${file.bytes != null}, size: ${file.size}");
    }
    debugPrint(
        "Starting upload of ${validFiles.length} files, $filesWithBytes have bytes data");

    // Show a dialog with the upload progress for multiple files
    await _showMultipleFileUploadDialog(
        context, ref, validFiles, isEPublishing);
  }

  /// Shows a dialog for selecting and uploading files
  Future<void> _showFileSelectionDialog(
    //BuildContext context,
    BuildContext outerContext,
    List<PlatformFile> selectedFiles,
    Map<PlatformFile, String> validFiles,
    WidgetRef ref,
    bool isEPublishing,
  ) async {
    List<String> unsupportedExtensions = [];
    bool isUploading = false;

    return showDialog(
      //context: context,
      context: outerContext,
      barrierDismissible: false,
      //builder: (BuildContext context) {

      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

            return AlertDialog(
              title: const Text(
                'Upload Files',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                  color: Colors.black87,
                ),
              ),
              titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
              contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
              content: Container(
                width: double.maxFinite,
                constraints: const BoxConstraints(maxHeight: 400),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Select files to upload. You can add multiple files.',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text(
                          'Selected files:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${selectedFiles.length} files',
                          style: const TextStyle(
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Divider(),
                    Expanded(
                      child: selectedFiles.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.file_copy_outlined,
                                    size: 48,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'No files selected yet',
                                    style: TextStyle(
                                      color: Colors.black54,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.add),
                                    label: const Text('Select File'),
                                    onPressed: isUploading
                                        ? null
                                        : () async {
                                            await _addFile(
                                                context,
                                                setState,
                                                selectedFiles,
                                                validFiles,
                                                unsupportedExtensions);
                                          },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: bookBranchGreen,
                                      foregroundColor: Colors.white,
                                      disabledBackgroundColor:
                                          Colors.grey.shade300,
                                      disabledForegroundColor:
                                          Colors.grey.shade600,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                      ),
                                      elevation: 1,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: selectedFiles.length,
                              itemBuilder: (context, index) {
                                final file = selectedFiles[index];
                                final fileType = validFiles[file] ?? 'Unknown';
                                final fileSize = _formatFileSize(file.size);

                                // Determine icon based on file type
                                IconData iconData;
                                switch (fileType) {
                                  case 'Image':
                                    iconData = Icons.image;
                                    break;
                                  case 'PDF':
                                    iconData = Icons.picture_as_pdf;
                                    break;
                                  case 'Video':
                                    iconData = Icons.video_file;
                                    break;
                                  default:
                                    iconData = Icons.insert_drive_file;
                                }

                                return Card(
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 4),
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    side: BorderSide(
                                      color: bookBranchGreen,
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(6.0),
                                  ),
                                  child: ListTile(
                                    leading:
                                        Icon(iconData, color: bookBranchGreen),
                                    title: Text(
                                      file.name,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                        color: Colors.black87,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    subtitle: Text(
                                      '$fileType • $fileSize',
                                      style: const TextStyle(
                                        color: Colors.black54,
                                      ),
                                    ),
                                    trailing: IconButton(
                                      icon: const Icon(Icons.delete,
                                          color: Colors.red),
                                      onPressed: isUploading
                                          ? null
                                          : () {
                                              setState(() {
                                                validFiles.remove(file);
                                                selectedFiles.removeAt(index);
                                              });
                                            },
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                if (selectedFiles.isNotEmpty)
                  TextButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('Select Another File'),
                    onPressed: isUploading
                        ? null
                        : () async {
                            await _addFile(context, setState, selectedFiles,
                                validFiles, unsupportedExtensions);
                          },
                    style: TextButton.styleFrom(
                      foregroundColor: bookBranchGreen,
                    ),
                  ),
                TextButton(
                  onPressed: isUploading
                      ? null
                      : () {
                          Navigator.of(context).pop();
                        },
                  style: TextButton.styleFrom(
                    foregroundColor: bookBranchGreen,
                  ),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: (selectedFiles.isEmpty || isUploading)
                      ? null
                      : () async {
                          debugPrint("Upload button pressed, starting upload");
                          setState(() {
                            isUploading = true;
                          });

                          // Small delay to ensure the UI updates
                          await Future.delayed(
                              const Duration(milliseconds: 100));

                          if (context.mounted) {
                            Navigator.of(context).pop();
                            // Call the process method directly with the selected files
                            await _processMultipleFileUpload(
                                outerContext, ref, Map.from(validFiles),
                                isEPublishing: isEPublishing);
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade300,
                    disabledForegroundColor: Colors.grey.shade600,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 1,
                  ),
                  child: isUploading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          selectedFiles.length == 1
                              ? 'Upload File'
                              : 'Upload ${selectedFiles.length} Files',
                          style: const TextStyle(
                            fontSize: 15,
                          ),
                        ),
                ),
              ],
              actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              shape: RoundedRectangleBorder(
                side: const BorderSide(color: bookBranchGreen, width: 1.5),
                borderRadius: BorderRadius.circular(8.0),
              ),
              backgroundColor: Colors.white,
              elevation: 3,
            );
          },
        );
      },
    );
  }

  /// Helper method to select a file and add it to the selection
  Future<void> _addFile(
    BuildContext context,
    StateSetter setState,
    List<PlatformFile> selectedFiles,
    Map<PlatformFile, String> validFiles,
    List<String> unsupportedExtensions,
  ) async {
    // Pick a single file
    PlatformFile? file = await dataObj.pickFile();

    // Check if the widget is still mounted before using context
    if (!context.mounted) return;

    if (file != null) {
      debugPrint(
          "Selected file: ${file.name}, size: ${file.size}, has bytes: ${file.bytes != null}");

      final fileType = _determineFileType(file.extension ?? '');
      if (fileType != 'Other') {
        setState(() {
          selectedFiles.add(file);
          validFiles[file] = fileType;
        });
        debugPrint("Added file to selection: ${file.name}, type: $fileType");
      } else {
        setState(() {
          unsupportedExtensions.add(file.extension ?? 'unknown');
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'The file type .${file.extension} is not supported. Please select an approved file type.'),
            ),
          );
        }
      }
    } else {
      debugPrint("No file was selected or file data is missing");
    }
  }

  /// Format file size to a human-readable string
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Shows a dialog with progress indicators for multiple file uploads
  Future<void> _showMultipleFileUploadDialog(
    BuildContext context,
    WidgetRef ref,
    Map<PlatformFile, String> files,
    bool isEPublishing,
  ) async {
    // Create a map to track progress for each file
    Map<PlatformFile, double> progressMap = {};
    Map<PlatformFile, bool> completedMap = {};
    Map<PlatformFile, bool> successMap = {};

    // Initialize progress for each file
    for (var file in files.keys) {
      progressMap[file] = 0.0;
      completedMap[file] = false;
      successMap[file] = false;
    }

    // Create notifiers for the UI
    ValueNotifier<Map<PlatformFile, double>> progressNotifier =
        ValueNotifier<Map<PlatformFile, double>>(Map.from(progressMap));
    ValueNotifier<Map<PlatformFile, bool>> completedNotifier =
        ValueNotifier<Map<PlatformFile, bool>>(Map.from(completedMap));
    ValueNotifier<Map<PlatformFile, bool>> successNotifier =
        ValueNotifier<Map<PlatformFile, bool>>(Map.from(successMap));
    ValueNotifier<int> totalCompletedNotifier = ValueNotifier<int>(0);

    // Show the dialog and make sure to await it
    // This ensures the dialog is shown before we start uploading
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // Start the upload process after the dialog is shown
        // Use a post-frame callback to ensure the dialog is fully built
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _startUploadProcess(
              context,
              ref,
              files,
              isEPublishing,
              progressNotifier,
              completedNotifier,
              successNotifier,
              totalCompletedNotifier);
        });

        const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

        return AlertDialog(
          title: const Text(
            'Uploading Files',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.black87,
            ),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
          content: SizedBox(
            width: double.maxFinite,
            child: ValueListenableBuilder<int>(
              valueListenable: totalCompletedNotifier,
              builder: (context, totalCompleted, child) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Uploading $totalCompleted/${files.length} files',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 10),
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: files.length,
                        itemBuilder: (context, index) {
                          PlatformFile file = files.keys.elementAt(index);
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  file.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                ValueListenableBuilder<
                                    Map<PlatformFile, double>>(
                                  valueListenable: progressNotifier,
                                  builder: (context, progressValues, _) {
                                    return LinearProgressIndicator(
                                      value: progressValues[file],
                                    );
                                  },
                                ),
                                const SizedBox(height: 4),
                                ValueListenableBuilder<Map<PlatformFile, bool>>(
                                  valueListenable: completedNotifier,
                                  builder: (context, completedValues, _) {
                                    return ValueListenableBuilder<
                                        Map<PlatformFile, bool>>(
                                      valueListenable: successNotifier,
                                      builder: (context, successValues, _) {
                                        if (completedValues[file] == true) {
                                          return Row(
                                            children: [
                                              Icon(
                                                successValues[file] == true
                                                    ? Icons.check_circle
                                                    : Icons.error,
                                                color:
                                                    successValues[file] == true
                                                        ? Colors.green
                                                        : Colors.red,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                successValues[file] == true
                                                    ? 'Completed'
                                                    : 'Failed',
                                                style: TextStyle(
                                                  color: successValues[file] ==
                                                          true
                                                      ? Colors.green
                                                      : Colors.red,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          );
                                        } else {
                                          return const Text(
                                            'Uploading...',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.black54,
                                            ),
                                          );
                                        }
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          actions: [
            ValueListenableBuilder<int>(
              valueListenable: totalCompletedNotifier,
              builder: (context, totalCompleted, _) {
                return TextButton(
                  onPressed: totalCompleted == files.length
                      ? () => Navigator.of(context).pop()
                      : null,
                  style: TextButton.styleFrom(
                    foregroundColor: bookBranchGreen,
                  ),
                  child: const Text(
                    'Close',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
            ),
          ],
          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: bookBranchGreen, width: 1.5),
            borderRadius: BorderRadius.circular(8.0),
          ),
          backgroundColor: Colors.white,
          elevation: 3,
        );
      },
    );

    // The upload process will be started by the post-frame callback
  }

  /// Helper method to start the upload process after the dialog is shown
  Future<void> _startUploadProcess(
    BuildContext context,
    WidgetRef ref,
    Map<PlatformFile, String> files,
    bool isEPublishing,
    ValueNotifier<Map<PlatformFile, double>> progressNotifier,
    ValueNotifier<Map<PlatformFile, bool>> completedNotifier,
    ValueNotifier<Map<PlatformFile, bool>> successNotifier,
    ValueNotifier<int> totalCompletedNotifier,
  ) async {
    // Create maps to track progress for each file
    Map<PlatformFile, double> progressMap = {};
    Map<PlatformFile, bool> completedMap = {};
    Map<PlatformFile, bool> successMap = {};

    // Initialize progress for each file
    for (var file in files.keys) {
      progressMap[file] = 0.0;
      completedMap[file] = false;
      successMap[file] = false;
    }

    // Start uploading files one by one
    int completedCount = 0;

    debugPrint("Starting to upload ${files.length} files");

    // Debug: Check if files have valid data before uploading
    for (var file in files.keys) {
      debugPrint(
          "Pre-upload check for ${file.name}: has bytes=${file.bytes != null}, has path=${file.path != null}, size=${file.size}");
    }

    for (var entry in files.entries) {
      PlatformFile file = entry.key;
      String fileType = entry.value;

      debugPrint(
          "Processing file: ${file.name}, type: $fileType, size: ${file.size}, has bytes: ${file.bytes != null}");

      try {
        // Update file type and name for the current file
        selectedFileType = fileType;
        fileName.text = file.name;

        // Update progress callback
        void updateProgress(double progress) {
          progressMap[file] = progress;
          progressNotifier.value = Map.from(progressMap);
          debugPrint(
              "Upload progress for ${file.name}: ${(progress * 100).toStringAsFixed(1)}%");
        }

        // Upload the file to Firebase Storage
        debugPrint("Starting Firebase Storage upload for ${file.name}");
        Map<String, String?> uploadResults =
            await dataObj.uploadFileToFirebaseStorage(
          file,
          ref,
          'files/',
          updateProgress,
        );

        // Extract the file URL and file path from the results map
        String? fileUrl = uploadResults['fileUrl'];
        String? filePath = uploadResults['filePath'];

        debugPrint(
            "Upload results for ${file.name}: URL=${fileUrl != null}, path=${filePath != null}");

        if (fileUrl != null) {
          // Get the appropriate chapter and subsection based on whether this is for e-publishing
          String chapterValue = isEPublishing
              ? ref.watch(currentEPublishingChapter)
              : ref.watch(currentChapter);
          String subsectionValue = isEPublishing
              ? ref.watch(currentEPublishingSubsection)
              : ref.watch(currentSubsection);

          // Get the file hash if available
          String? fileHash = uploadResults['fileHash'];

          // Upload metadata to Firestore
          bool uploadSuccess = await dataObj.uploadData(
            ref,
            projectName: ref.read(currentProjectName.notifier).state,
            fileDescription: 'default description',
            displayName: file.name,
            fileType: fileType,
            fileName: file.name,
            privacy: selectedPrivacy,
            tags: [fileType],
            projectID: ref.read(currentProjectID.notifier).state,
            currentChapter: chapterValue,
            currentSubsection: subsectionValue,
            userID: auth.currentUser!.uid,
            fileURL: fileUrl,
            filePath: filePath,
            fileSize:
                0, // Set to 0 initially, will be updated by the Cloud Function
            fileHash: fileHash, // Pass the file hash for duplicate detection
          );

          if (uploadSuccess) {
            // Create a new FileMetadata object
            FileMetadata newFile = FileMetadata(
              fileDescription: 'default description',
              fileName: file.name,
              fileType: fileType,
              privacy: selectedPrivacy,
              tags: [fileType],
              projectID: ref.read(currentProjectID.notifier).state,
              displayName: file.name,
              fileURL: fileUrl,
              filePath: filePath,
              projectDescription: 'default description',
              projectName: ref.read(currentProjectName.notifier).state,
              uid: auth.currentUser!.uid,
            );

            // Update fileListProvider by adding the new file
            List<FileMetadata> currentFiles =
                ref.read(fileListProvider.notifier).state;
            ref.read(fileListProvider.notifier).state = [
              ...currentFiles,
              newFile
            ];

            // Update storage usage
            try {
              final FirebaseFunctions functions = FirebaseFunctions.instance;
              final HttpsCallable callable =
                  functions.httpsCallable('updateStorageOnFilesUpload');
              await callable.call({
                'fileSize': file.size,
                'userId': auth.currentUser!.uid,
                'fileUrl': fileUrl,
                'projectId': ref.watch(currentProjectID),
              });
            } catch (e) {
              debugPrint('Error calling function: $e');
            }

            // Mark as successful
            successMap[file] = true;
          } else {
            // Mark as failed
            successMap[file] = false;
          }
        } else {
          // Mark as failed
          successMap[file] = false;
        }
      } catch (e) {
        // Mark as failed
        successMap[file] = false;
        debugPrint('Error uploading file ${file.name}: $e');
      } finally {
        // Mark as completed
        completedMap[file] = true;
        completedCount++;

        // Update notifiers
        completedNotifier.value = Map.from(completedMap);
        successNotifier.value = Map.from(successMap);
        totalCompletedNotifier.value = completedCount;
      }
    }

    // Refresh the appropriate provider based on whether this is for e-publishing
    if (isEPublishing) {
      ref.invalidate(numberOfFilesByEPublishingSubsectionProvider);
      var _ = ref.refresh(numberOfFilesByEPublishingSubsectionProvider);
    } else {
      ref.invalidate(numberOfFilesBySubsectionProvider);
      var _ = ref.refresh(numberOfFilesBySubsectionProvider);
    }

    // Wait for user to close the dialog
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Uploaded ${successMap.values.where((success) => success).length}/${files.length} files successfully'),
        ),
      );
    }
  }

  /// Determines the file type based on the file extension
  String _determineFileType(String extension) {
    extension = extension.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif'].contains(extension)) {
      return 'Image';
    } else if (extension == 'pdf') {
      return 'PDF';
    } else if (['mp4', 'mov', 'avi'].contains(extension)) {
      return 'Video';
    } else {
      return 'Other';
    }
  }

  // Removed unused _uploadFile method as we now use _processMultipleFileUpload for all uploads
}
